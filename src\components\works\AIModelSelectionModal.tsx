/**
 * AI模型选择模态窗口组件
 */
import React, { useState } from 'react';
import { SimpleModal } from '@/components/common/modals';
import { MODELS } from '@/lib/AIserver';

interface AIModelSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (model: string) => void;
  selectedModel: string;
}

// 模型配置信息
const MODEL_CONFIG = {
  [MODELS.LLM_TEST]: {
    name: '测试版',
    description: '免费的AI模型，适合日常写作和润色需求',
    features: ['完全免费', '响应速度快', '适合基础创作'],
    icon: 'science',
    color: 'text-blue-600',
    bgColor: 'bg-blue-50',
    borderColor: 'border-blue-200',
    cost: '免费',
    costColor: 'text-green-600'
  },
  [MODELS.LLM_CLAUDE]: {
    name: '克劳德',
    description: 'Claude Sonnet 4，顶级推理能力和创作质量',
    features: ['顶级AI模型', '卓越推理能力', '高质量创作'],
    icon: 'psychology',
    color: 'text-purple-600',
    bgColor: 'bg-purple-50',
    borderColor: 'border-purple-200',
    cost: '5X消耗',
    costColor: 'text-orange-600'
  }
};

/**
 * AI模型选择模态窗口组件
 */
export const AIModelSelectionModal: React.FC<AIModelSelectionModalProps> = ({
  isOpen,
  onClose,
  onSelect,
  selectedModel
}) => {
  const [tempSelectedModel, setTempSelectedModel] = useState(selectedModel);

  // 处理模型选择
  const handleModelSelect = (model: string) => {
    setTempSelectedModel(model);
  };

  // 处理确认选择
  const handleConfirm = () => {
    onSelect(tempSelectedModel);
    onClose();
  };

  // 处理取消
  const handleCancel = () => {
    setTempSelectedModel(selectedModel); // 重置为原来的选择
    onClose();
  };

  return (
    <SimpleModal
      isOpen={isOpen}
      onClose={handleCancel}
      title="选择AI模型"
      maxWidth="max-w-2xl"
      footer={
        <div className="flex justify-end space-x-3">
          <button
            onClick={handleConfirm}
            className="px-4 py-2 bg-[#5a9d6b] text-white rounded-lg hover:bg-[#4a8d5b] transition-colors font-medium"
          >
            确认选择
          </button>
          <button
            onClick={handleCancel}
            className="px-4 py-2 border border-[rgba(120,180,140,0.3)] text-[#6d5c4d] rounded-lg hover:bg-[rgba(120,180,140,0.1)] transition-colors font-medium"
          >
            取消
          </button>
        </div>
      }
    >
      <div className="space-y-4">
        {Object.entries(MODEL_CONFIG).map(([modelKey, config]) => {
          const isSelected = tempSelectedModel === modelKey;
          
          return (
            <div
              key={modelKey}
              onClick={() => handleModelSelect(modelKey)}
              className={`relative p-4 rounded-xl border-2 cursor-pointer transition-all duration-300 transform hover:scale-[1.02] ${
                isSelected
                  ? `${config.bgColor} ${config.borderColor} shadow-lg ring-2 ring-opacity-20`
                  : 'bg-white border-gray-200 hover:border-gray-300 hover:shadow-md'
              }`}
            >
              {/* 选中状态指示器 */}
              {isSelected && (
                <div className="absolute top-3 right-3">
                  <span className="material-icons text-[#5a9d6b] text-xl">
                    check_circle
                  </span>
                </div>
              )}

              <div className="flex items-start space-x-4">
                {/* 模型图标 */}
                <div className={`flex-shrink-0 w-12 h-12 rounded-full ${config.bgColor} flex items-center justify-center`}>
                  <span className={`material-icons text-2xl ${config.color}`}>
                    {config.icon}
                  </span>
                </div>

                {/* 模型信息 */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-lg font-semibold text-[#4b3b2a]">
                      {config.name}
                    </h3>
                    <span className={`text-sm font-medium px-2 py-1 rounded-full ${
                      config.cost === '免费' 
                        ? 'bg-green-100 text-green-700' 
                        : 'bg-orange-100 text-orange-700'
                    }`}>
                      {config.cost}
                    </span>
                  </div>
                  
                  <p className="text-[#6d5c4d] text-sm mb-3 leading-relaxed">
                    {config.description}
                  </p>
                  
                  {/* 特性列表 */}
                  <div className="flex flex-wrap gap-2">
                    {config.features.map((feature, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2 py-1 rounded-md bg-[#f7f2ea] text-[#6d5c4d] text-xs font-medium"
                      >
                        <span className="material-icons text-xs mr-1 text-[#5a9d6b]">
                          check
                        </span>
                        {feature}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          );
        })}
        
        {/* 提示信息 */}
        <div className="mt-6 p-3 bg-[#f7f2ea] rounded-lg border border-[rgba(120,180,140,0.15)]">
          <div className="flex items-start space-x-2">
            <span className="material-icons text-[#5a9d6b] text-sm mt-0.5">
              info
            </span>
            <div className="text-xs text-[#6d5c4d] leading-relaxed">
              <p className="mb-1">
                <strong>消耗说明：</strong>不同模型有不同的字符消耗倍数
              </p>
              <p>
                • 测试版：完全免费使用，无消耗限制<br/>
                • 克劳德：高级模型，消耗为普通模型的5倍
              </p>
            </div>
          </div>
        </div>
      </div>
    </SimpleModal>
  );
};
