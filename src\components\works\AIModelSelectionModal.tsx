/**
 * AI模型选择模态窗口组件
 */
import React, { useState } from 'react';
import SimpleModal from '@/components/common/modals/SimpleModal';
import { MODELS } from '@/lib/AIserver';

interface AIModelSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (model: string) => void;
  selectedModel: string;
}

// 模型配置信息
const MODEL_CONFIG = {
  [MODELS.LLM_TEST]: {
    name: '测试版',
    subtitle: 'Test Model',
    description: '免费的AI模型，适合日常写作和润色需求，响应迅速且稳定可靠',
    features: ['完全免费使用', '响应速度快', '适合基础创作', '稳定可靠'],
    icon: 'science',
    color: '#3B82F6',
    bgGradient: 'from-blue-50 to-blue-100',
    borderColor: 'border-blue-200',
    shadowColor: 'shadow-blue-100',
    cost: '免费',
    costBg: 'bg-emerald-100',
    costText: 'text-emerald-700',
    iconBg: 'bg-blue-100'
  },
  [MODELS.LLM_CLAUDE]: {
    name: '克劳德',
    subtitle: 'Claude Sonnet 4',
    description: '顶级AI模型，拥有卓越的推理能力和创作质量，适合高要求的专业创作',
    features: ['顶级AI模型', '卓越推理能力', '高质量创作', '专业级输出'],
    icon: 'psychology',
    color: '#8B5CF6',
    bgGradient: 'from-purple-50 to-purple-100',
    borderColor: 'border-purple-200',
    shadowColor: 'shadow-purple-100',
    cost: '5X消耗',
    costBg: 'bg-amber-100',
    costText: 'text-amber-700',
    iconBg: 'bg-purple-100'
  }
};

/**
 * AI模型选择模态窗口组件
 */
export const AIModelSelectionModal: React.FC<AIModelSelectionModalProps> = ({
  isOpen,
  onClose,
  onSelect,
  selectedModel
}) => {
  const [tempSelectedModel, setTempSelectedModel] = useState(selectedModel);

  // 处理模型选择
  const handleModelSelect = (model: string) => {
    setTempSelectedModel(model);
  };

  // 处理确认选择
  const handleConfirm = () => {
    onSelect(tempSelectedModel);
    onClose();
  };

  // 处理取消
  const handleCancel = () => {
    setTempSelectedModel(selectedModel); // 重置为原来的选择
    onClose();
  };

  return (
    <SimpleModal
      isOpen={isOpen}
      onClose={handleCancel}
      title={
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-gradient-to-br from-[#5a9d6b] to-[#4a8d5b] rounded-lg flex items-center justify-center shadow-md">
            <span className="material-icons text-white text-lg">model_training</span>
          </div>
          <span className="text-xl font-bold text-[#4b3b2a]">选择AI模型</span>
        </div>
      }
      maxWidth="max-w-3xl"
      footer={
        <div className="flex justify-end space-x-4">
          <button
            onClick={handleConfirm}
            className="group relative px-6 py-3 bg-gradient-to-r from-[#5a9d6b] to-[#4a8d5b] text-white rounded-xl font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 overflow-hidden"
          >
            <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <span className="relative flex items-center space-x-2">
              <span className="material-icons text-sm">check</span>
              <span>确认选择</span>
            </span>
          </button>
          <button
            onClick={handleCancel}
            className="group px-6 py-3 border-2 border-[rgba(120,180,140,0.3)] text-[#6d5c4d] rounded-xl font-semibold hover:bg-[rgba(120,180,140,0.1)] hover:border-[rgba(120,180,140,0.5)] transform hover:scale-105 transition-all duration-300"
          >
            <span className="flex items-center space-x-2">
              <span className="material-icons text-sm">close</span>
              <span>取消</span>
            </span>
          </button>
        </div>
      }
    >
      <div className="space-y-6">
        {Object.entries(MODEL_CONFIG).map(([modelKey, config]) => {
          const isSelected = tempSelectedModel === modelKey;

          return (
            <div
              key={modelKey}
              onClick={() => handleModelSelect(modelKey)}
              className={`group relative overflow-hidden rounded-2xl border-2 cursor-pointer transition-all duration-500 transform hover:scale-[1.02] hover:rotate-1 ${
                isSelected
                  ? `bg-gradient-to-br ${config.bgGradient} ${config.borderColor} shadow-2xl ${config.shadowColor} ring-4 ring-opacity-30 ring-[#5a9d6b]`
                  : 'bg-gradient-to-br from-white to-gray-50 border-gray-200 hover:border-gray-300 hover:shadow-xl hover:from-gray-50 hover:to-white'
              }`}
              style={{
                boxShadow: isSelected
                  ? `0 20px 40px -12px ${config.color}20, 0 8px 16px -4px ${config.color}10`
                  : '0 4px 20px -4px rgba(0, 0, 0, 0.1)'
              }}
            >
              {/* 装饰性背景图案 */}
              <div className="absolute top-0 right-0 w-32 h-32 opacity-5">
                <div className="w-full h-full rounded-full" style={{ backgroundColor: config.color }}></div>
              </div>

              {/* 选中状态指示器 */}
              {isSelected && (
                <div className="absolute top-4 right-4 z-10">
                  <div className="w-8 h-8 bg-[#5a9d6b] rounded-full flex items-center justify-center shadow-lg animate-pulse">
                    <span className="material-icons text-white text-lg">
                      check
                    </span>
                  </div>
                </div>
              )}

              <div className="relative p-6">
                <div className="flex items-start space-x-5">
                  {/* 模型图标 */}
                  <div className={`flex-shrink-0 w-16 h-16 rounded-2xl ${config.iconBg} flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                    <span className="material-icons text-3xl" style={{ color: config.color }}>
                      {config.icon}
                    </span>
                  </div>

                  {/* 模型信息 */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <h3 className="text-xl font-bold text-[#4b3b2a] mb-1 group-hover:text-[#3a2b1a] transition-colors">
                          {config.name}
                        </h3>
                        <p className="text-sm text-[#8a7c70] font-medium">
                          {config.subtitle}
                        </p>
                      </div>
                      <div className={`px-3 py-1.5 rounded-full ${config.costBg} ${config.costText} text-sm font-bold shadow-sm`}>
                        {config.cost}
                      </div>
                    </div>

                    <p className="text-[#6d5c4d] text-sm mb-4 leading-relaxed">
                      {config.description}
                    </p>

                    {/* 特性列表 */}
                    <div className="grid grid-cols-2 gap-2">
                      {config.features.map((feature, index) => (
                        <div
                          key={index}
                          className="flex items-center space-x-2 px-3 py-2 rounded-lg bg-white/60 backdrop-blur-sm border border-white/40 shadow-sm group-hover:bg-white/80 transition-all duration-300"
                        >
                          <div className="w-1.5 h-1.5 rounded-full" style={{ backgroundColor: config.color }}></div>
                          <span className="text-xs font-medium text-[#6d5c4d]">
                            {feature}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* 底部装饰线 */}
              {isSelected && (
                <div
                  className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r opacity-60"
                  style={{
                    backgroundImage: `linear-gradient(90deg, ${config.color}, ${config.color}80, ${config.color})`
                  }}
                ></div>
              )}
            </div>
          );
        })}

        {/* 提示信息 */}
        <div className="mt-8 p-5 bg-gradient-to-r from-[#f7f2ea] to-[#f0e9df] rounded-2xl border border-[rgba(120,180,140,0.2)] shadow-inner">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-[#5a9d6b] rounded-full flex items-center justify-center shadow-sm">
              <span className="material-icons text-white text-sm">
                lightbulb
              </span>
            </div>
            <div className="flex-1">
              <h4 className="text-sm font-semibold text-[#4b3b2a] mb-2">消耗说明</h4>
              <div className="text-xs text-[#6d5c4d] leading-relaxed space-y-2">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-emerald-400 rounded-full"></div>
                  <span><strong>测试版：</strong>完全免费使用，无消耗限制，适合日常创作</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-amber-400 rounded-full"></div>
                  <span><strong>克劳德：</strong>高级模型，消耗为普通模型的5倍，质量更高</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </SimpleModal>
  );
};
